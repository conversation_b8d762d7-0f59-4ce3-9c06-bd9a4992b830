<?php

namespace App\Filament\Exports;

use App\Models\Trip;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class TripsReportExporter extends Exporter
{
    protected static ?string $model = Trip::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('Trip ID'),

            ExportColumn::make('status')
                ->label('Status')
                ->formatStateUsing(fn ($state) => $state?->getLabel() ?? 'Unknown'),

            ExportColumn::make('rider.user.name')
                ->label('Rider First Name'),

            ExportColumn::make('rider.user.last_name')
                ->label('Rider Last Name'),

            ExportColumn::make('rider.user.phone_number')
                ->label('Rider Phone'),

            ExportColumn::make('driver.user.name')
                ->label('Driver First Name'),

            ExportColumn::make('driver.user.last_name')
                ->label('Driver Last Name'),

            ExportColumn::make('driver.user.phone_number')
                ->label('Driver Phone'),

            ExportColumn::make('vehicle.license_plate_number')
                ->label('Vehicle License Plate'),

            ExportColumn::make('distance')
                ->label('Distance (km)')
                ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) : 'N/A'),

            ExportColumn::make('trip_location.departure_address')
                ->label('Departure Address'),

            ExportColumn::make('trip_location.arrival_address')
                ->label('Arrival Address'),

            ExportColumn::make('departure_area.name')
                ->label('Departure Area'),

            ExportColumn::make('arrival_area.name')
                ->label('Arrival Area'),

            ExportColumn::make('estimated_departure_time')
                ->label('Estimated Departure')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('actual_departure_time')
                ->label('Actual Departure')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('estimated_arrival_time')
                ->label('Estimated Arrival')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('actual_arrival_time')
                ->label('Actual Arrival')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('trip_duration')
                ->label('Trip Duration (minutes)')
                ->state(function (Trip $record): string {
                    if ($record->actual_departure_time && $record->actual_arrival_time) {
                        $duration = $record->actual_departure_time->diffInMinutes($record->actual_arrival_time);

                        return (string) $duration;
                    }

                    return 'N/A';
                }),

            ExportColumn::make('base_fare')
                ->label('Base Fare (LYD)')
                ->state(function (Trip $record): string {
                    if ($record->pricing_breakdown) {
                        $pricing = json_decode($record->pricing_breakdown, true);

                        return number_format($pricing['base_fare'] ?? 0, 2);
                    }

                    return '0.00';
                }),

            ExportColumn::make('distance_fare')
                ->label('Distance Fare (LYD)')
                ->state(function (Trip $record): string {
                    if ($record->pricing_breakdown) {
                        $pricing = json_decode($record->pricing_breakdown, true);

                        return number_format($pricing['distance_fare'] ?? 0, 2);
                    }

                    return '0.00';
                }),

            ExportColumn::make('surge_multiplier')
                ->label('Surge Multiplier')
                ->state(function (Trip $record): string {
                    if ($record->pricing_breakdown) {
                        $pricing = json_decode($record->pricing_breakdown, true);

                        return (string) ($pricing['surge_multiplier'] ?? 1);
                    }

                    return '1';
                }),

            ExportColumn::make('total_fare')
                ->label('Total Fare (LYD)')
                ->state(function (Trip $record): string {
                    if ($record->pricing_breakdown) {
                        $pricing = json_decode($record->pricing_breakdown, true);

                        return number_format($pricing['total'] ?? 0, 2);
                    }

                    return '0.00';
                }),

            ExportColumn::make('is_favorite')
                ->label('Is Favorite')
                ->formatStateUsing(fn ($state) => $state ? 'Yes' : 'No'),

            ExportColumn::make('is_female')
                ->label('Female Driver Requested')
                ->formatStateUsing(fn ($state) => $state ? 'Yes' : 'No'),

            ExportColumn::make('cancelled_by')
                ->label('Cancelled By'),

            ExportColumn::make('cancellation_stage')
                ->label('Cancellation Stage'),

            ExportColumn::make('rider_notes')
                ->label('Rider Notes'),

            ExportColumn::make('vehicle_type.name')
                ->label('Vehicle Type'),

            ExportColumn::make('trip_rating.rating')
                ->label('Trip Rating')
                ->formatStateUsing(fn ($state) => $state ? $state.'/5' : 'No rating'),

            ExportColumn::make('trip_rating.comment')
                ->label('Trip Rating Comment'),

            ExportColumn::make('created_at')
                ->label('Trip Created')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('updated_at')
                ->label('Last Updated')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('payment_status')
                ->label('Payment Status')
                ->state(function (Trip $record): string {
                    // Assuming payment relationship exists
                    return $record->payment?->status?->getLabel() ?? 'No payment';
                }),

            ExportColumn::make('contacted_drivers_count')
                ->label('Drivers Contacted')
                ->state(function (Trip $record): string {
                    if ($record->contacted_drivers) {
                        $contacted = is_string($record->contacted_drivers)
                            ? json_decode($record->contacted_drivers, true)
                            : $record->contacted_drivers;

                        return is_array($contacted) ? (string) count($contacted) : '0';
                    }

                    return '0';
                }),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your trips report export has completed and '.number_format($export->successful_rows).' '.str('row')->plural($export->successful_rows).' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' '.number_format($failedRowsCount).' '.str('row')->plural($failedRowsCount).' failed to export.';
        }

        return $body;
    }

    public function getFileName(Export $export): string
    {
        return "trips-report-{$export->getKey()}.csv";
    }
}
